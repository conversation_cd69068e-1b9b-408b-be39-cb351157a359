import type { LoaderFunctionArgs } from "@remix-run/node";
import { Link as RemixLink } from "@remix-run/react";
import {
  Page,
  Layout,
  Text,
  Card,
  Button,
  BlockStack,
  InlineStack,
  Grid,
  Icon,
  Badge,
} from "@shopify/polaris";
import { 
  LockIcon,
  ShieldIcon,
  BackupIcon,
  EyeIcon,
  AlertTriangleIcon,
  HistoryIcon
} from "@shopify/polaris-icons";
import { TitleBar } from "@shopify/app-bridge-react";
import { authenticate } from "../shopify.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  await authenticate.admin(request);
  return null;
};

export default function SecurityIndex() {
  const securityTools = [
    {
      title: "Security Scanner",
      description: "Scan your store for common security vulnerabilities and get recommendations",
      icon: ShieldIcon,
      url: "/app/security/scanner",
      features: ["Vulnerability scanning", "Security recommendations", "Compliance checks", "Risk assessment"],
      status: "ready"
    },
    {
      title: "Access Monitor",
      description: "Monitor admin access, track changes, and maintain security logs",
      icon: EyeIcon,
      url: "/app/security/access",
      features: ["Access logging", "Change tracking", "User monitoring", "Security alerts"],
      status: "ready"
    },
    {
      title: "Backup Manager",
      description: "Automated backup scheduling with secure storage and easy restoration",
      icon: BackupIcon,
      url: "/app/security/backups",
      features: ["Automated backups", "Secure storage", "Easy restoration", "Backup verification"],
      status: "ready"
    },
    {
      title: "Permission Auditor",
      description: "Review and audit staff permissions and access levels across your store",
      icon: LockIcon,
      url: "/app/security/permissions",
      features: ["Permission review", "Access auditing", "Role management", "Security recommendations"],
      status: "beta"
    },
    {
      title: "Change Log Tracker",
      description: "Track all store modifications with detailed logs and rollback capabilities",
      icon: HistoryIcon,
      url: "/app/security/changelog",
      features: ["Change tracking", "Detailed logs", "Rollback options", "Audit trails"],
      status: "beta"
    },
    {
      title: "Security Alerts",
      description: "Real-time security alerts for suspicious activities and potential threats",
      icon: AlertTriangleIcon,
      url: "/app/security/alerts",
      features: ["Real-time alerts", "Threat detection", "Suspicious activity monitoring", "Incident response"],
      status: "ready"
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "ready":
        return <Badge tone="success">Ready</Badge>;
      case "beta":
        return <Badge tone="attention">Beta</Badge>;
      case "coming-soon":
        return <Badge>Coming Soon</Badge>;
      default:
        return <Badge>Unknown</Badge>;
    }
  };

  return (
    <Page>
      <TitleBar title="Security & Backup Tools" />
      <BlockStack gap="500">
        <Layout>
          <Layout.Section>
            <Card>
              <BlockStack gap="400">
                <BlockStack gap="200">
                  <InlineStack align="space-between">
                    <InlineStack gap="200" align="center">
                      <Icon source={LockIcon} tone="base" />
                      <Text as="h2" variant="headingLg">
                        Security & Backup Utilities
                      </Text>
                    </InlineStack>
                    <RemixLink to="/app">
                      <Button variant="plain">← Back to Dashboard</Button>
                    </RemixLink>
                  </InlineStack>
                  <Text variant="bodyMd" tone="subdued">
                    Protect your store with comprehensive security tools. Monitor access, scan for vulnerabilities, 
                    manage backups, and maintain detailed audit trails for complete peace of mind.
                  </Text>
                </BlockStack>

                <Grid>
                  {securityTools.map((tool, index) => (
                    <Grid.Cell key={index} columnSpan={{ xs: 6, sm: 6, md: 4, lg: 4, xl: 4 }}>
                      <Card>
                        <BlockStack gap="400">
                          <InlineStack align="space-between">
                            <Icon source={tool.icon} tone="base" />
                            {getStatusBadge(tool.status)}
                          </InlineStack>
                          
                          <BlockStack gap="200">
                            <Text as="h3" variant="headingMd">
                              {tool.title}
                            </Text>
                            <Text variant="bodyMd" tone="subdued">
                              {tool.description}
                            </Text>
                          </BlockStack>

                          <BlockStack gap="200">
                            <Text variant="headingSm">Key Features:</Text>
                            <BlockStack gap="100">
                              {tool.features.map((feature, featureIndex) => (
                                <Text key={featureIndex} variant="bodySm" tone="subdued">
                                  • {feature}
                                </Text>
                              ))}
                            </BlockStack>
                          </BlockStack>

                          <InlineStack align="end">
                            <RemixLink to={tool.url}>
                              <Button variant="primary" size="slim">
                                Open Tool →
                              </Button>
                            </RemixLink>
                          </InlineStack>
                        </BlockStack>
                      </Card>
                    </Grid.Cell>
                  ))}
                </Grid>
              </BlockStack>
            </Card>
          </Layout.Section>

          <Layout.Section variant="oneThird">
            <BlockStack gap="500">
              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingMd">
                    Security Status
                  </Text>
                  <BlockStack gap="200">
                    <InlineStack align="space-between">
                      <Text variant="bodyMd">Security Score</Text>
                      <Badge tone="success">95/100</Badge>
                    </InlineStack>
                    <InlineStack align="space-between">
                      <Text variant="bodyMd">Last Scan</Text>
                      <Badge>2 hours ago</Badge>
                    </InlineStack>
                    <InlineStack align="space-between">
                      <Text variant="bodyMd">Vulnerabilities</Text>
                      <Badge tone="success">0 Found</Badge>
                    </InlineStack>
                    <InlineStack align="space-between">
                      <Text variant="bodyMd">Last Backup</Text>
                      <Badge tone="success">Today</Badge>
                    </InlineStack>
                  </BlockStack>
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingMd">
                    Quick Actions
                  </Text>
                  <BlockStack gap="300">
                    <RemixLink to="/app/security/scanner">
                      <Button fullWidth>
                        Run Security Scan
                      </Button>
                    </RemixLink>
                    <RemixLink to="/app/security/backups">
                      <Button fullWidth variant="secondary">
                        Create Backup
                      </Button>
                    </RemixLink>
                    <RemixLink to="/app/security/access">
                      <Button fullWidth variant="secondary">
                        View Access Logs
                      </Button>
                    </RemixLink>
                  </BlockStack>
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingMd">
                    Security Tips
                  </Text>
                  <BlockStack gap="200">
                    <Text variant="bodySm" tone="subdued">• Run security scans weekly</Text>
                    <Text variant="bodySm" tone="subdued">• Keep backups up to date</Text>
                    <Text variant="bodySm" tone="subdued">• Monitor access logs regularly</Text>
                    <Text variant="bodySm" tone="subdued">• Review staff permissions monthly</Text>
                    <Text variant="bodySm" tone="subdued">• Enable two-factor authentication</Text>
                  </BlockStack>
                </BlockStack>
              </Card>
            </BlockStack>
          </Layout.Section>
        </Layout>
      </BlockStack>
    </Page>
  );
}
