import type { LoaderFunctionArgs } from "@remix-run/node";
import { Link as RemixLink } from "@remix-run/react";
import {
  Page,
  Layout,
  Text,
  Card,
  Button,
  BlockStack,
  InlineStack,
  Grid,
  Icon,
  Badge,
  ProgressBar,
} from "@shopify/polaris";
import { 
  AnalyticsIcon,
  ChartIcon,
  ReportIcon,
  TrendingUpIcon,
  CalendarIcon,
  ExportIcon
} from "@shopify/polaris-icons";
import { TitleBar } from "@shopify/app-bridge-react";
import { authenticate } from "../shopify.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  await authenticate.admin(request);
  return null;
};

export default function AnalyticsIndex() {
  const analyticsTools = [
    {
      title: "Custom Dashboard",
      description: "Create personalized dashboards with KPIs and metrics that matter to your business",
      icon: ChartIcon,
      url: "/app/analytics/dashboard",
      features: ["Custom KPIs", "Real-time data", "Interactive charts", "Mobile responsive"],
      status: "ready"
    },
    {
      title: "Sales Analytics",
      description: "Advanced sales reporting with trends, forecasting, and performance analysis",
      icon: TrendingUpIcon,
      url: "/app/analytics/sales",
      features: ["Sales trends", "Forecasting", "Product performance", "Revenue analysis"],
      status: "ready"
    },
    {
      title: "Product Performance",
      description: "Analyze your best and worst performing products with detailed insights",
      icon: ReportIcon,
      url: "/app/analytics/products",
      features: ["Performance ranking", "Inventory turnover", "Profit margins", "Category analysis"],
      status: "ready"
    },
    {
      title: "Traffic Analytics",
      description: "Understand your store traffic patterns and conversion optimization opportunities",
      icon: AnalyticsIcon,
      url: "/app/analytics/traffic",
      features: ["Traffic sources", "Conversion rates", "User behavior", "Page performance"],
      status: "beta"
    },
    {
      title: "Profit Margin Calculator",
      description: "Real-time profit analysis with cost tracking and margin optimization",
      icon: CalendarIcon,
      url: "/app/analytics/profit",
      features: ["Real-time margins", "Cost tracking", "Optimization tips", "Trend analysis"],
      status: "ready"
    },
    {
      title: "Comparative Reports",
      description: "Period-over-period comparisons with detailed variance analysis",
      icon: ExportIcon,
      url: "/app/analytics/comparative",
      features: ["Period comparison", "Variance analysis", "Growth metrics", "Seasonal trends"],
      status: "ready"
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "ready":
        return <Badge tone="success">Ready</Badge>;
      case "beta":
        return <Badge tone="attention">Beta</Badge>;
      case "coming-soon":
        return <Badge>Coming Soon</Badge>;
      default:
        return <Badge>Unknown</Badge>;
    }
  };

  // Mock analytics data
  const quickStats = [
    { label: "Total Revenue", value: "$45,231", change: "+12.5%", trend: "up" },
    { label: "Orders Today", value: "127", change: "+8.2%", trend: "up" },
    { label: "Conversion Rate", value: "3.4%", change: "-0.3%", trend: "down" },
    { label: "Avg Order Value", value: "$89.50", change: "+5.1%", trend: "up" }
  ];

  return (
    <Page>
      <TitleBar title="Analytics & Reporting" />
      <BlockStack gap="500">
        <Layout>
          <Layout.Section>
            <Card>
              <BlockStack gap="400">
                <BlockStack gap="200">
                  <InlineStack align="space-between">
                    <InlineStack gap="200" align="center">
                      <Icon source={AnalyticsIcon} tone="base" />
                      <Text as="h2" variant="headingLg">
                        Analytics & Reporting Tools
                      </Text>
                    </InlineStack>
                    <RemixLink to="/app">
                      <Button variant="plain">← Back to Dashboard</Button>
                    </RemixLink>
                  </InlineStack>
                  <Text variant="bodyMd" tone="subdued">
                    Comprehensive analytics and reporting tools to track performance, identify trends, 
                    and make data-driven decisions for your store.
                  </Text>
                </BlockStack>

                {/* Quick Stats Overview */}
                <Grid>
                  {quickStats.map((stat, index) => (
                    <Grid.Cell key={index} columnSpan={{ xs: 6, sm: 3, md: 3, lg: 3, xl: 3 }}>
                      <Card>
                        <BlockStack gap="200">
                          <Text variant="bodySm" tone="subdued">{stat.label}</Text>
                          <Text as="h3" variant="headingLg">{stat.value}</Text>
                          <InlineStack gap="100" align="center">
                            <Text 
                              variant="bodySm" 
                              tone={stat.trend === "up" ? "success" : "critical"}
                            >
                              {stat.change}
                            </Text>
                            <Text variant="bodySm" tone="subdued">vs last period</Text>
                          </InlineStack>
                        </BlockStack>
                      </Card>
                    </Grid.Cell>
                  ))}
                </Grid>

                <Grid>
                  {analyticsTools.map((tool, index) => (
                    <Grid.Cell key={index} columnSpan={{ xs: 6, sm: 6, md: 4, lg: 4, xl: 4 }}>
                      <Card>
                        <BlockStack gap="400">
                          <InlineStack align="space-between">
                            <Icon source={tool.icon} tone="base" />
                            {getStatusBadge(tool.status)}
                          </InlineStack>
                          
                          <BlockStack gap="200">
                            <Text as="h3" variant="headingMd">
                              {tool.title}
                            </Text>
                            <Text variant="bodyMd" tone="subdued">
                              {tool.description}
                            </Text>
                          </BlockStack>

                          <BlockStack gap="200">
                            <Text variant="headingSm">Key Features:</Text>
                            <BlockStack gap="100">
                              {tool.features.map((feature, featureIndex) => (
                                <Text key={featureIndex} variant="bodySm" tone="subdued">
                                  • {feature}
                                </Text>
                              ))}
                            </BlockStack>
                          </BlockStack>

                          <InlineStack align="end">
                            <RemixLink to={tool.url}>
                              <Button variant="primary" size="slim">
                                Open Tool →
                              </Button>
                            </RemixLink>
                          </InlineStack>
                        </BlockStack>
                      </Card>
                    </Grid.Cell>
                  ))}
                </Grid>
              </BlockStack>
            </Card>
          </Layout.Section>

          <Layout.Section variant="oneThird">
            <BlockStack gap="500">
              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingMd">
                    Quick Actions
                  </Text>
                  <BlockStack gap="300">
                    <RemixLink to="/app/analytics/dashboard">
                      <Button fullWidth>
                        View Dashboard
                      </Button>
                    </RemixLink>
                    <RemixLink to="/app/analytics/sales">
                      <Button fullWidth variant="secondary">
                        Sales Report
                      </Button>
                    </RemixLink>
                    <RemixLink to="/app/analytics/products">
                      <Button fullWidth variant="secondary">
                        Product Analysis
                      </Button>
                    </RemixLink>
                  </BlockStack>
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingMd">
                    Report Generation
                  </Text>
                  <Text variant="bodyMd" tone="subdued">
                    Generate comprehensive reports for different time periods and metrics.
                  </Text>
                  <BlockStack gap="200">
                    <ProgressBar progress={75} size="small" />
                    <Text variant="bodySm" tone="subdued">
                      Monthly report: 75% complete
                    </Text>
                  </BlockStack>
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingMd">
                    Analytics Stats
                  </Text>
                  <BlockStack gap="200">
                    <InlineStack align="space-between">
                      <Text variant="bodyMd">Available Tools</Text>
                      <Text variant="bodyMd" fontWeight="semibold">{analyticsTools.length}</Text>
                    </InlineStack>
                    <InlineStack align="space-between">
                      <Text variant="bodyMd">Ready Tools</Text>
                      <Text variant="bodyMd" fontWeight="semibold">
                        {analyticsTools.filter(tool => tool.status === "ready").length}
                      </Text>
                    </InlineStack>
                    <InlineStack align="space-between">
                      <Text variant="bodyMd">Data Points</Text>
                      <Text variant="bodyMd" fontWeight="semibold">1,247</Text>
                    </InlineStack>
                  </BlockStack>
                </BlockStack>
              </Card>
            </BlockStack>
          </Layout.Section>
        </Layout>
      </BlockStack>
    </Page>
  );
}
