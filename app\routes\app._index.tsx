import type { LoaderFunctionArgs } from "@remix-run/node";
import { Link as RemixLink } from "@remix-run/react";
import {
  Page,
  Layout,
  Text,
  Card,
  Button,
  BlockStack,
  Box,
  List,
  Link,
  InlineStack,
  Grid,
  Icon,
} from "@shopify/polaris";
import {
  ProductIcon,
  OrderIcon,
  CustomerIcon,
  AnalyticsIcon,
  SearchIcon,
  ImportIcon,
  ToolsIcon,
  AutomationIcon,
  LockIcon
} from "@shopify/polaris-icons";
import { TitleBar } from "@shopify/app-bridge-react";
import { authenticate } from "../shopify.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  await authenticate.admin(request);

  return null;
};



export default function Index() {

  const utilities = [
    {
      title: "Product Management",
      description: "Bulk edit products, manage inventory, optimize images",
      icon: ProductIcon,
      url: "/app/products",
      tools: ["Bulk Editor", "Inventory Sync", "Image Optimizer", "Variant Manager", "Price Calculator"]
    },
    {
      title: "Order Management",
      description: "Automate orders, bulk processing, analytics",
      icon: OrderIcon,
      url: "/app/orders",
      tools: ["Order Automation", "Bulk Processing", "Order Analytics", "Fulfillment Optimizer"]
    },
    {
      title: "Customer Management",
      description: "Segment customers, bulk operations, analytics",
      icon: CustomerIcon,
      url: "/app/customers",
      tools: ["Customer Segmentation", "Bulk Operations", "Customer Analytics", "Data Cleaner"]
    },
    {
      title: "Analytics & Reporting",
      description: "Custom dashboards, sales analytics, performance tracking",
      icon: AnalyticsIcon,
      url: "/app/analytics",
      tools: ["Custom Dashboard", "Sales Analytics", "Product Performance", "Traffic Analytics"]
    },
    {
      title: "SEO & Marketing",
      description: "Meta tag management, URL optimization, marketing tools",
      icon: SearchIcon,
      url: "/app/seo",
      tools: ["Meta Tag Manager", "URL Optimizer", "Sitemap Generator", "Schema Markup"]
    },
    {
      title: "Data Import/Export",
      description: "Import/export data, backup management, migration tools",
      icon: ImportIcon,
      url: "/app/data",
      tools: ["Universal Importer", "Custom Exports", "Backup Creator", "Migration Assistant"]
    },
    {
      title: "Store Maintenance",
      description: "Clean up duplicates, validate data, performance monitoring",
      icon: ToolsIcon,
      url: "/app/maintenance",
      tools: ["Duplicate Detector", "Data Validator", "Performance Monitor", "Cleanup Wizard"]
    },
    {
      title: "Automation & Workflows",
      description: "Task scheduling, workflow automation, alerts",
      icon: AutomationIcon,
      url: "/app/automation",
      tools: ["Task Scheduler", "Workflow Builder", "Alert System", "Auto-Tagging"]
    },
    {
      title: "Security & Backup",
      description: "Security scanning, backup management, access monitoring",
      icon: LockIcon,
      url: "/app/security",
      tools: ["Security Scanner", "Backup Manager", "Access Monitor", "Permission Auditor"]
    }
  ];

  return (
    <Page>
      <TitleBar title="Swiss Knife Utility App" />
      <BlockStack gap="500">
        <Layout>
          <Layout.Section>
            <Card>
              <BlockStack gap="500">
                <BlockStack gap="200">
                  <Text as="h2" variant="headingLg">
                    Welcome to Swiss Knife Utility App 🛠️
                  </Text>
                  <Text variant="bodyMd" as="p">
                    Your all-in-one toolkit for Shopify store management. Access powerful utilities
                    to streamline your operations, boost productivity, and optimize your store performance.
                  </Text>
                </BlockStack>
                <Text as="h3" variant="headingMd">
                  Available Utilities
                </Text>
                <Grid>
                  {utilities.map((utility, index) => (
                    <Grid.Cell key={index} columnSpan={{ xs: 6, sm: 3, md: 2, lg: 2, xl: 2 }}>
                      <Card>
                        <BlockStack gap="300">
                          <InlineStack align="space-between">
                            <Icon source={utility.icon} tone="base" />
                            <RemixLink to={utility.url}>
                              <Button variant="plain" size="slim">
                                Open →
                              </Button>
                            </RemixLink>
                          </InlineStack>
                          <BlockStack gap="200">
                            <Text as="h4" variant="headingSm">
                              {utility.title}
                            </Text>
                            <Text variant="bodySm" tone="subdued">
                              {utility.description}
                            </Text>
                            <Box>
                              <Text variant="captionMd" tone="subdued">
                                Tools: {utility.tools.slice(0, 2).join(", ")}
                                {utility.tools.length > 2 && ` +${utility.tools.length - 2} more`}
                              </Text>
                            </Box>
                          </BlockStack>
                        </BlockStack>
                      </Card>
                    </Grid.Cell>
                  ))}
                </Grid>
              </BlockStack>
            </Card>
          </Layout.Section>
          <Layout.Section variant="oneThird">
            <BlockStack gap="500">
              <Card>
                <BlockStack gap="200">
                  <Text as="h2" variant="headingMd">
                    Quick Stats
                  </Text>
                  <BlockStack gap="200">
                    <InlineStack align="space-between">
                      <Text as="span" variant="bodyMd">
                        Total Utilities
                      </Text>
                      <Text as="span" variant="bodyMd" fontWeight="semibold">
                        {utilities.length}
                      </Text>
                    </InlineStack>
                    <InlineStack align="space-between">
                      <Text as="span" variant="bodyMd">
                        Categories
                      </Text>
                      <Text as="span" variant="bodyMd" fontWeight="semibold">
                        9
                      </Text>
                    </InlineStack>
                    <InlineStack align="space-between">
                      <Text as="span" variant="bodyMd">
                        Total Tools
                      </Text>
                      <Text as="span" variant="bodyMd" fontWeight="semibold">
                        {utilities.reduce((acc, utility) => acc + utility.tools.length, 0)}
                      </Text>
                    </InlineStack>
                  </BlockStack>
                </BlockStack>
              </Card>
              <Card>
                <BlockStack gap="200">
                  <Text as="h2" variant="headingMd">
                    Getting Started
                  </Text>
                  <List>
                    <List.Item>
                      Start with{" "}
                      <RemixLink to="/app/products">
                        <Link removeUnderline>
                          Product Management
                        </Link>
                      </RemixLink>{" "}
                      for bulk operations
                    </List.Item>
                    <List.Item>
                      Explore Shopify’s API with{" "}
                      <Link
                        url="https://shopify.dev/docs/apps/tools/graphiql-admin-api"
                        target="_blank"
                        removeUnderline
                      >
                        GraphiQL
                      </Link>
                    </List.Item>
                    <List.Item>
                      Set up{" "}
                      <RemixLink to="/app/automation">
                        <Link removeUnderline>
                          Automation
                        </Link>
                      </RemixLink>{" "}
                      to save time
                    </List.Item>
                  </List>
                </BlockStack>
              </Card>
            </BlockStack>
          </Layout.Section>
        </Layout>
      </BlockStack>
    </Page>
  );
}
