import type { LoaderFunctionArgs } from "@remix-run/node";
import { Link as RemixLink } from "@remix-run/react";
import {
  Page,
  Layout,
  Text,
  Card,
  Button,
  BlockStack,
  InlineStack,
  Grid,
  Icon,
  Badge,
} from "@shopify/polaris";
import { 
  AutomationIcon,
  CalendarIcon,
  AlertTriangleIcon,
  TagIcon,
  NotificationIcon,
  AnalyticsIcon
} from "@shopify/polaris-icons";
import { TitleBar } from "@shopify/app-bridge-react";
import { authenticate } from "../shopify.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  await authenticate.admin(request);
  return null;
};

export default function AutomationIndex() {
  const automationTools = [
    {
      title: "Task Scheduler",
      description: "Schedule recurring tasks and operations to run automatically at specified times",
      icon: CalendarIcon,
      url: "/app/automation/scheduler",
      features: ["Recurring schedules", "Task monitoring", "Email notifications", "Failure handling"],
      status: "ready"
    },
    {
      title: "Workflow Builder",
      description: "Create custom automation workflows with triggers, conditions, and actions",
      icon: AutomationIcon,
      url: "/app/automation/workflows",
      features: ["Visual workflow builder", "Custom triggers", "Conditional logic", "Multi-step actions"],
      status: "ready"
    },
    {
      title: "Alert System",
      description: "Set up intelligent alerts for important store events and threshold breaches",
      icon: AlertTriangleIcon,
      url: "/app/automation/alerts",
      features: ["Custom alerts", "Threshold monitoring", "Multi-channel notifications", "Alert history"],
      status: "ready"
    },
    {
      title: "Auto-Tagging Rules",
      description: "Automatically tag products, orders, and customers based on custom criteria",
      icon: TagIcon,
      url: "/app/automation/tagging",
      features: ["Smart tagging", "Rule-based automation", "Bulk operations", "Tag management"],
      status: "ready"
    },
    {
      title: "Inventory Alerts",
      description: "Monitor inventory levels and automate reorder notifications and processes",
      icon: NotificationIcon,
      url: "/app/automation/inventory",
      features: ["Low stock alerts", "Reorder automation", "Supplier notifications", "Inventory forecasting"],
      status: "beta"
    },
    {
      title: "Performance Automation",
      description: "Automate performance monitoring and optimization tasks for your store",
      icon: AnalyticsIcon,
      url: "/app/automation/performance",
      features: ["Performance monitoring", "Auto-optimization", "Report generation", "Trend analysis"],
      status: "beta"
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "ready":
        return <Badge tone="success">Ready</Badge>;
      case "beta":
        return <Badge tone="attention">Beta</Badge>;
      case "coming-soon":
        return <Badge>Coming Soon</Badge>;
      default:
        return <Badge>Unknown</Badge>;
    }
  };

  return (
    <Page>
      <TitleBar title="Automation & Workflow Tools" />
      <BlockStack gap="500">
        <Layout>
          <Layout.Section>
            <Card>
              <BlockStack gap="400">
                <BlockStack gap="200">
                  <InlineStack align="space-between">
                    <InlineStack gap="200" align="center">
                      <Icon source={AutomationIcon} tone="base" />
                      <Text as="h2" variant="headingLg">
                        Automation & Workflow Utilities
                      </Text>
                    </InlineStack>
                    <RemixLink to="/app">
                      <Button variant="plain">← Back to Dashboard</Button>
                    </RemixLink>
                  </InlineStack>
                  <Text variant="bodyMd" tone="subdued">
                    Automate repetitive tasks and create intelligent workflows. Schedule operations, 
                    set up alerts, and build custom automation rules to save time and improve efficiency.
                  </Text>
                </BlockStack>

                <Grid>
                  {automationTools.map((tool, index) => (
                    <Grid.Cell key={index} columnSpan={{ xs: 6, sm: 6, md: 4, lg: 4, xl: 4 }}>
                      <Card>
                        <BlockStack gap="400">
                          <InlineStack align="space-between">
                            <Icon source={tool.icon} tone="base" />
                            {getStatusBadge(tool.status)}
                          </InlineStack>
                          
                          <BlockStack gap="200">
                            <Text as="h3" variant="headingMd">
                              {tool.title}
                            </Text>
                            <Text variant="bodyMd" tone="subdued">
                              {tool.description}
                            </Text>
                          </BlockStack>

                          <BlockStack gap="200">
                            <Text variant="headingSm">Key Features:</Text>
                            <BlockStack gap="100">
                              {tool.features.map((feature, featureIndex) => (
                                <Text key={featureIndex} variant="bodySm" tone="subdued">
                                  • {feature}
                                </Text>
                              ))}
                            </BlockStack>
                          </BlockStack>

                          <InlineStack align="end">
                            <RemixLink to={tool.url}>
                              <Button variant="primary" size="slim">
                                Open Tool →
                              </Button>
                            </RemixLink>
                          </InlineStack>
                        </BlockStack>
                      </Card>
                    </Grid.Cell>
                  ))}
                </Grid>
              </BlockStack>
            </Card>
          </Layout.Section>

          <Layout.Section variant="oneThird">
            <BlockStack gap="500">
              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingMd">
                    Active Automations
                  </Text>
                  <BlockStack gap="200">
                    <InlineStack align="space-between">
                      <Text variant="bodyMd">Total Rules</Text>
                      <Badge tone="info">12</Badge>
                    </InlineStack>
                    <InlineStack align="space-between">
                      <Text variant="bodyMd">Active Rules</Text>
                      <Badge tone="success">10</Badge>
                    </InlineStack>
                    <InlineStack align="space-between">
                      <Text variant="bodyMd">Scheduled Tasks</Text>
                      <Badge>5</Badge>
                    </InlineStack>
                    <InlineStack align="space-between">
                      <Text variant="bodyMd">Alerts Set</Text>
                      <Badge tone="attention">8</Badge>
                    </InlineStack>
                  </BlockStack>
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingMd">
                    Quick Actions
                  </Text>
                  <BlockStack gap="300">
                    <RemixLink to="/app/automation/workflows">
                      <Button fullWidth>
                        Create Workflow
                      </Button>
                    </RemixLink>
                    <RemixLink to="/app/automation/scheduler">
                      <Button fullWidth variant="secondary">
                        Schedule Task
                      </Button>
                    </RemixLink>
                    <RemixLink to="/app/automation/alerts">
                      <Button fullWidth variant="secondary">
                        Setup Alerts
                      </Button>
                    </RemixLink>
                  </BlockStack>
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingMd">
                    Recent Activity
                  </Text>
                  <BlockStack gap="200">
                    <Text variant="bodySm" tone="subdued">• Order tagging rule executed (5 min ago)</Text>
                    <Text variant="bodySm" tone="subdued">• Inventory alert triggered (1 hour ago)</Text>
                    <Text variant="bodySm" tone="subdued">• Weekly report generated (Yesterday)</Text>
                    <Text variant="bodySm" tone="subdued">• Price update automation ran (2 days ago)</Text>
                  </BlockStack>
                </BlockStack>
              </Card>
            </BlockStack>
          </Layout.Section>
        </Layout>
      </BlockStack>
    </Page>
  );
}
