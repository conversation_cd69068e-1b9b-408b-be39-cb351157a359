import type { LoaderFunctionArgs } from "@remix-run/node";
import { Link as RemixLink } from "@remix-run/react";
import {
  Page,
  Layout,
  Text,
  Card,
  Button,
  BlockStack,
  InlineStack,
  Grid,
  Icon,
  Badge,
} from "@shopify/polaris";
import { 
  SearchIcon,
  EditIcon,
  LinkIcon,
  CodeIcon,
  ShareIcon,
  AnalyticsIcon
} from "@shopify/polaris-icons";
import { TitleBar } from "@shopify/app-bridge-react";
import { authenticate } from "../shopify.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  await authenticate.admin(request);
  return null;
};

export default function SEOIndex() {
  const seoTools = [
    {
      title: "Meta Tag Manager",
      description: "Bulk edit meta titles, descriptions, and keywords for better search engine visibility",
      icon: EditIcon,
      url: "/app/seo/meta-manager",
      features: ["Bulk meta editing", "SEO scoring", "Character limits", "Preview snippets"],
      status: "ready"
    },
    {
      title: "URL Optimizer",
      description: "Clean up and optimize product and page URLs for better SEO performance",
      icon: LinkIcon,
      url: "/app/seo/url-optimizer",
      features: ["URL cleanup", "Redirect management", "Slug optimization", "Broken link detection"],
      status: "ready"
    },
    {
      title: "Sitemap Generator",
      description: "Generate and manage XML sitemaps for better search engine crawling",
      icon: CodeIcon,
      url: "/app/seo/sitemap",
      features: ["XML sitemap generation", "Auto-updates", "Priority settings", "Submission tracking"],
      status: "ready"
    },
    {
      title: "Schema Markup Tool",
      description: "Add structured data to products and pages for rich search results",
      icon: SearchIcon,
      url: "/app/seo/schema",
      features: ["Product schema", "Review markup", "Breadcrumb schema", "Organization data"],
      status: "beta"
    },
    {
      title: "Social Media Optimizer",
      description: "Optimize images and content for social media sharing and engagement",
      icon: ShareIcon,
      url: "/app/seo/social",
      features: ["Open Graph tags", "Twitter cards", "Image optimization", "Social previews"],
      status: "ready"
    },
    {
      title: "SEO Analytics",
      description: "Track SEO performance with rankings, traffic, and optimization recommendations",
      icon: AnalyticsIcon,
      url: "/app/seo/analytics",
      features: ["Ranking tracking", "Traffic analysis", "SEO scoring", "Recommendations"],
      status: "beta"
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "ready":
        return <Badge tone="success">Ready</Badge>;
      case "beta":
        return <Badge tone="attention">Beta</Badge>;
      case "coming-soon":
        return <Badge>Coming Soon</Badge>;
      default:
        return <Badge>Unknown</Badge>;
    }
  };

  return (
    <Page>
      <TitleBar title="SEO & Marketing Tools" />
      <BlockStack gap="500">
        <Layout>
          <Layout.Section>
            <Card>
              <BlockStack gap="400">
                <BlockStack gap="200">
                  <InlineStack align="space-between">
                    <InlineStack gap="200" align="center">
                      <Icon source={SearchIcon} tone="base" />
                      <Text as="h2" variant="headingLg">
                        SEO & Marketing Utilities
                      </Text>
                    </InlineStack>
                    <RemixLink to="/app">
                      <Button variant="plain">← Back to Dashboard</Button>
                    </RemixLink>
                  </InlineStack>
                  <Text variant="bodyMd" tone="subdued">
                    Optimize your store for search engines and social media. Manage meta tags, 
                    URLs, sitemaps, and structured data to improve your online visibility.
                  </Text>
                </BlockStack>

                <Grid>
                  {seoTools.map((tool, index) => (
                    <Grid.Cell key={index} columnSpan={{ xs: 6, sm: 6, md: 4, lg: 4, xl: 4 }}>
                      <Card>
                        <BlockStack gap="400">
                          <InlineStack align="space-between">
                            <Icon source={tool.icon} tone="base" />
                            {getStatusBadge(tool.status)}
                          </InlineStack>
                          
                          <BlockStack gap="200">
                            <Text as="h3" variant="headingMd">
                              {tool.title}
                            </Text>
                            <Text variant="bodyMd" tone="subdued">
                              {tool.description}
                            </Text>
                          </BlockStack>

                          <BlockStack gap="200">
                            <Text variant="headingSm">Key Features:</Text>
                            <BlockStack gap="100">
                              {tool.features.map((feature, featureIndex) => (
                                <Text key={featureIndex} variant="bodySm" tone="subdued">
                                  • {feature}
                                </Text>
                              ))}
                            </BlockStack>
                          </BlockStack>

                          <InlineStack align="end">
                            <RemixLink to={tool.url}>
                              <Button variant="primary" size="slim">
                                Open Tool →
                              </Button>
                            </RemixLink>
                          </InlineStack>
                        </BlockStack>
                      </Card>
                    </Grid.Cell>
                  ))}
                </Grid>
              </BlockStack>
            </Card>
          </Layout.Section>

          <Layout.Section variant="oneThird">
            <BlockStack gap="500">
              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingMd">
                    Quick Actions
                  </Text>
                  <BlockStack gap="300">
                    <RemixLink to="/app/seo/meta-manager">
                      <Button fullWidth>
                        Optimize Meta Tags
                      </Button>
                    </RemixLink>
                    <RemixLink to="/app/seo/url-optimizer">
                      <Button fullWidth variant="secondary">
                        Clean URLs
                      </Button>
                    </RemixLink>
                    <RemixLink to="/app/seo/sitemap">
                      <Button fullWidth variant="secondary">
                        Generate Sitemap
                      </Button>
                    </RemixLink>
                  </BlockStack>
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingMd">
                    SEO Health Score
                  </Text>
                  <BlockStack gap="200">
                    <InlineStack align="space-between">
                      <Text variant="bodyMd">Overall Score</Text>
                      <Badge tone="success">85/100</Badge>
                    </InlineStack>
                    <InlineStack align="space-between">
                      <Text variant="bodyMd">Meta Tags</Text>
                      <Badge tone="attention">Good</Badge>
                    </InlineStack>
                    <InlineStack align="space-between">
                      <Text variant="bodyMd">URL Structure</Text>
                      <Badge tone="success">Excellent</Badge>
                    </InlineStack>
                    <InlineStack align="space-between">
                      <Text variant="bodyMd">Schema Markup</Text>
                      <Badge tone="critical">Needs Work</Badge>
                    </InlineStack>
                  </BlockStack>
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingMd">
                    SEO Tips
                  </Text>
                  <BlockStack gap="200">
                    <Text variant="bodySm" tone="subdued">• Optimize meta descriptions for better CTR</Text>
                    <Text variant="bodySm" tone="subdued">• Add schema markup to products</Text>
                    <Text variant="bodySm" tone="subdued">• Clean up duplicate content</Text>
                    <Text variant="bodySm" tone="subdued">• Improve page loading speed</Text>
                    <Text variant="bodySm" tone="subdued">• Add alt text to all images</Text>
                  </BlockStack>
                </BlockStack>
              </Card>
            </BlockStack>
          </Layout.Section>
        </Layout>
      </BlockStack>
    </Page>
  );
}
