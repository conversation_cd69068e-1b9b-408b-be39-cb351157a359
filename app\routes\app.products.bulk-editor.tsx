import { useState, useCallback } from "react";
import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useActionData, useSubmit, Link as RemixLink } from "@remix-run/react";
import {
  Page,
  Layout,
  Text,
  Card,
  Button,
  BlockStack,
  InlineStack,
  TextField,
  Select,
  Checkbox,
  DataTable,
  Badge,
  Banner,
  Spinner,
  Modal,
  TextContainer,
} from "@shopify/polaris";
import { TitleBar, useAppBridge } from "@shopify/app-bridge-react";
import { authenticate } from "../shopify.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { admin } = await authenticate.admin(request);
  
  // Fetch products for bulk editing
  const response = await admin.graphql(`
    #graphql
    query getProducts($first: Int!) {
      products(first: $first) {
        edges {
          node {
            id
            title
            handle
            status
            vendor
            productType
            tags
            priceRangeV2 {
              minVariantPrice {
                amount
                currencyCode
              }
            }
            featuredImage {
              url
              altText
            }
            totalInventory
          }
        }
        pageInfo {
          hasNextPage
          endCursor
        }
      }
    }
  `, {
    variables: { first: 50 }
  });

  const { data } = await response.json();
  return json({ products: data.products.edges });
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const { admin } = await authenticate.admin(request);
  const formData = await request.formData();
  const action = formData.get("action");

  if (action === "bulkUpdate") {
    const selectedProducts = JSON.parse(formData.get("selectedProducts") as string);
    const updateData = JSON.parse(formData.get("updateData") as string);
    
    const results = [];
    
    for (const productId of selectedProducts) {
      try {
        const mutation = `
          #graphql
          mutation productUpdate($input: ProductInput!) {
            productUpdate(input: $input) {
              product {
                id
                title
                vendor
                productType
                tags
              }
              userErrors {
                field
                message
              }
            }
          }
        `;

        const variables = {
          input: {
            id: productId,
            ...updateData
          }
        };

        const response = await admin.graphql(mutation, { variables });
        const { data } = await response.json();
        
        if (data.productUpdate.userErrors.length > 0) {
          results.push({
            productId,
            success: false,
            errors: data.productUpdate.userErrors
          });
        } else {
          results.push({
            productId,
            success: true,
            product: data.productUpdate.product
          });
        }
      } catch (error) {
        results.push({
          productId,
          success: false,
          errors: [{ message: "Failed to update product" }]
        });
      }
    }

    return json({ 
      success: true, 
      results,
      message: `Updated ${results.filter(r => r.success).length} of ${results.length} products`
    });
  }

  return json({ success: false, message: "Invalid action" });
};

export default function BulkProductEditor() {
  const actionData = useActionData<typeof action>();
  const submit = useSubmit();
  const shopify = useAppBridge();
  
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [bulkUpdateData, setBulkUpdateData] = useState({
    vendor: "",
    productType: "",
    tags: "",
    status: ""
  });
  const [showUpdateModal, setShowUpdateModal] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  const handleBulkUpdate = useCallback(() => {
    if (selectedProducts.length === 0) {
      shopify.toast.show("Please select products to update", { isError: true });
      return;
    }

    setIsUpdating(true);
    const formData = new FormData();
    formData.append("action", "bulkUpdate");
    formData.append("selectedProducts", JSON.stringify(selectedProducts));
    
    // Filter out empty values
    const updateData = Object.fromEntries(
      Object.entries(bulkUpdateData).filter(([_, value]) => value !== "")
    );
    
    if (updateData.tags) {
      updateData.tags = updateData.tags.split(",").map(tag => tag.trim());
    }
    
    formData.append("updateData", JSON.stringify(updateData));
    
    submit(formData, { method: "post" });
    setShowUpdateModal(false);
    setIsUpdating(false);
  }, [selectedProducts, bulkUpdateData, submit, shopify]);

  const productRows = [
    // Mock data for demonstration
    ["Product 1", "Active", "Vendor A", "Type A", "$29.99", "10"],
    ["Product 2", "Draft", "Vendor B", "Type B", "$39.99", "5"],
    ["Product 3", "Active", "Vendor A", "Type C", "$19.99", "15"],
  ];

  return (
    <Page>
      <TitleBar title="Bulk Product Editor" />
      <BlockStack gap="500">
        {actionData?.success && (
          <Banner tone="success" title="Bulk Update Complete">
            <p>{actionData.message}</p>
          </Banner>
        )}
        
        {actionData?.success === false && (
          <Banner tone="critical" title="Update Failed">
            <p>{actionData.message}</p>
          </Banner>
        )}

        <Layout>
          <Layout.Section>
            <Card>
              <BlockStack gap="400">
                <InlineStack align="space-between">
                  <Text as="h2" variant="headingLg">
                    Bulk Product Editor
                  </Text>
                  <RemixLink to="/app/products">
                    <Button variant="plain">← Back to Products</Button>
                  </RemixLink>
                </InlineStack>
                
                <Text variant="bodyMd" tone="subdued">
                  Select multiple products and update their properties in bulk. 
                  You can modify vendor, product type, tags, and status for multiple products at once.
                </Text>

                <InlineStack gap="300">
                  <Button 
                    variant="primary" 
                    onClick={() => setShowUpdateModal(true)}
                    disabled={selectedProducts.length === 0}
                  >
                    Update Selected ({selectedProducts.length})
                  </Button>
                  <Button onClick={() => setSelectedProducts([])}>
                    Clear Selection
                  </Button>
                </InlineStack>

                <DataTable
                  columnContentTypes={['text', 'text', 'text', 'text', 'text', 'text']}
                  headings={['Product', 'Status', 'Vendor', 'Type', 'Price', 'Inventory']}
                  rows={productRows}
                  selectable
                />
              </BlockStack>
            </Card>
          </Layout.Section>

          <Layout.Section variant="oneThird">
            <BlockStack gap="500">
              <Card>
                <BlockStack gap="300">
                  <Text as="h3" variant="headingMd">
                    Bulk Update Fields
                  </Text>
                  
                  <TextField
                    label="Vendor"
                    value={bulkUpdateData.vendor}
                    onChange={(value) => setBulkUpdateData(prev => ({ ...prev, vendor: value }))}
                    placeholder="Leave empty to keep current"
                    autoComplete="off"
                  />
                  
                  <TextField
                    label="Product Type"
                    value={bulkUpdateData.productType}
                    onChange={(value) => setBulkUpdateData(prev => ({ ...prev, productType: value }))}
                    placeholder="Leave empty to keep current"
                    autoComplete="off"
                  />
                  
                  <TextField
                    label="Tags (comma separated)"
                    value={bulkUpdateData.tags}
                    onChange={(value) => setBulkUpdateData(prev => ({ ...prev, tags: value }))}
                    placeholder="tag1, tag2, tag3"
                    autoComplete="off"
                  />
                  
                  <Select
                    label="Status"
                    options={[
                      { label: "Keep current", value: "" },
                      { label: "Active", value: "ACTIVE" },
                      { label: "Draft", value: "DRAFT" },
                      { label: "Archived", value: "ARCHIVED" }
                    ]}
                    value={bulkUpdateData.status}
                    onChange={(value) => setBulkUpdateData(prev => ({ ...prev, status: value }))}
                  />
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingMd">
                    Quick Stats
                  </Text>
                  <InlineStack align="space-between">
                    <Text variant="bodyMd">Selected Products</Text>
                    <Badge tone="info">{selectedProducts.length}</Badge>
                  </InlineStack>
                  <InlineStack align="space-between">
                    <Text variant="bodyMd">Total Products</Text>
                    <Badge>{productRows.length}</Badge>
                  </InlineStack>
                </BlockStack>
              </Card>
            </BlockStack>
          </Layout.Section>
        </Layout>

        <Modal
          open={showUpdateModal}
          onClose={() => setShowUpdateModal(false)}
          title="Confirm Bulk Update"
          primaryAction={{
            content: "Update Products",
            onAction: handleBulkUpdate,
            loading: isUpdating
          }}
          secondaryActions={[
            {
              content: "Cancel",
              onAction: () => setShowUpdateModal(false)
            }
          ]}
        >
          <Modal.Section>
            <TextContainer>
              <p>
                You are about to update {selectedProducts.length} products with the following changes:
              </p>
              <ul>
                {bulkUpdateData.vendor && <li>Vendor: {bulkUpdateData.vendor}</li>}
                {bulkUpdateData.productType && <li>Product Type: {bulkUpdateData.productType}</li>}
                {bulkUpdateData.tags && <li>Tags: {bulkUpdateData.tags}</li>}
                {bulkUpdateData.status && <li>Status: {bulkUpdateData.status}</li>}
              </ul>
              <p>This action cannot be undone. Are you sure you want to continue?</p>
            </TextContainer>
          </Modal.Section>
        </Modal>
      </BlockStack>
    </Page>
  );
}
