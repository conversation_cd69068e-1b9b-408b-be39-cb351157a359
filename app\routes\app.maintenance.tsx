import type { LoaderFunctionArgs } from "@remix-run/node";
import { Link as RemixLink } from "@remix-run/react";
import {
  Page,
  Layout,
  Text,
  Card,
  Button,
  BlockStack,
  InlineStack,
  Grid,
  Icon,
  Badge,
} from "@shopify/polaris";
import { 
  ToolsIcon,
  CleanIcon,
  SearchIcon,
  AnalyticsIcon,
  RefreshIcon,
  AlertTriangleIcon
} from "@shopify/polaris-icons";
import { TitleBar } from "@shopify/app-bridge-react";
import { authenticate } from "../shopify.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  await authenticate.admin(request);
  return null;
};

export default function MaintenanceIndex() {
  const maintenanceTools = [
    {
      title: "Duplicate Detector",
      description: "Find and merge duplicate products, customers, and other data across your store",
      icon: SearchIcon,
      url: "/app/maintenance/duplicates",
      features: ["Product duplicates", "Customer merging", "Smart matching", "Bulk operations"],
      status: "ready"
    },
    {
      title: "Data Validator",
      description: "Validate data integrity and identify inconsistencies in your store data",
      icon: AlertTriangleIcon,
      url: "/app/maintenance/validator",
      features: ["Data validation", "Error detection", "Integrity checks", "Repair suggestions"],
      status: "ready"
    },
    {
      title: "Performance Monitor",
      description: "Monitor app and store performance with detailed metrics and alerts",
      icon: AnalyticsIcon,
      url: "/app/maintenance/performance",
      features: ["Performance metrics", "Load time tracking", "Resource monitoring", "Alerts"],
      status: "beta"
    },
    {
      title: "Cleanup Wizard",
      description: "Remove unused tags, collections, and optimize your store's data structure",
      icon: CleanIcon,
      url: "/app/maintenance/cleanup",
      features: ["Unused tag removal", "Collection optimization", "Data cleanup", "Storage optimization"],
      status: "ready"
    },
    {
      title: "Link Checker",
      description: "Scan for broken internal and external links throughout your store",
      icon: RefreshIcon,
      url: "/app/maintenance/links",
      features: ["Broken link detection", "Internal link audit", "External link validation", "Fix suggestions"],
      status: "ready"
    },
    {
      title: "Health Dashboard",
      description: "Comprehensive store health overview with actionable recommendations",
      icon: ToolsIcon,
      url: "/app/maintenance/health",
      features: ["Health scoring", "Issue prioritization", "Recommendations", "Progress tracking"],
      status: "ready"
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "ready":
        return <Badge tone="success">Ready</Badge>;
      case "beta":
        return <Badge tone="attention">Beta</Badge>;
      case "coming-soon":
        return <Badge>Coming Soon</Badge>;
      default:
        return <Badge>Unknown</Badge>;
    }
  };

  return (
    <Page>
      <TitleBar title="Store Maintenance Tools" />
      <BlockStack gap="500">
        <Layout>
          <Layout.Section>
            <Card>
              <BlockStack gap="400">
                <BlockStack gap="200">
                  <InlineStack align="space-between">
                    <InlineStack gap="200" align="center">
                      <Icon source={ToolsIcon} tone="base" />
                      <Text as="h2" variant="headingLg">
                        Store Maintenance Utilities
                      </Text>
                    </InlineStack>
                    <RemixLink to="/app">
                      <Button variant="plain">← Back to Dashboard</Button>
                    </RemixLink>
                  </InlineStack>
                  <Text variant="bodyMd" tone="subdued">
                    Keep your store running smoothly with maintenance tools. Clean up duplicates, 
                    validate data, monitor performance, and maintain optimal store health.
                  </Text>
                </BlockStack>

                <Grid>
                  {maintenanceTools.map((tool, index) => (
                    <Grid.Cell key={index} columnSpan={{ xs: 6, sm: 6, md: 4, lg: 4, xl: 4 }}>
                      <Card>
                        <BlockStack gap="400">
                          <InlineStack align="space-between">
                            <Icon source={tool.icon} tone="base" />
                            {getStatusBadge(tool.status)}
                          </InlineStack>
                          
                          <BlockStack gap="200">
                            <Text as="h3" variant="headingMd">
                              {tool.title}
                            </Text>
                            <Text variant="bodyMd" tone="subdued">
                              {tool.description}
                            </Text>
                          </BlockStack>

                          <BlockStack gap="200">
                            <Text variant="headingSm">Key Features:</Text>
                            <BlockStack gap="100">
                              {tool.features.map((feature, featureIndex) => (
                                <Text key={featureIndex} variant="bodySm" tone="subdued">
                                  • {feature}
                                </Text>
                              ))}
                            </BlockStack>
                          </BlockStack>

                          <InlineStack align="end">
                            <RemixLink to={tool.url}>
                              <Button variant="primary" size="slim">
                                Open Tool →
                              </Button>
                            </RemixLink>
                          </InlineStack>
                        </BlockStack>
                      </Card>
                    </Grid.Cell>
                  ))}
                </Grid>
              </BlockStack>
            </Card>
          </Layout.Section>

          <Layout.Section variant="oneThird">
            <BlockStack gap="500">
              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingMd">
                    Store Health Score
                  </Text>
                  <BlockStack gap="200">
                    <InlineStack align="space-between">
                      <Text variant="bodyMd">Overall Health</Text>
                      <Badge tone="success">92/100</Badge>
                    </InlineStack>
                    <InlineStack align="space-between">
                      <Text variant="bodyMd">Data Quality</Text>
                      <Badge tone="success">Excellent</Badge>
                    </InlineStack>
                    <InlineStack align="space-between">
                      <Text variant="bodyMd">Performance</Text>
                      <Badge tone="attention">Good</Badge>
                    </InlineStack>
                    <InlineStack align="space-between">
                      <Text variant="bodyMd">Duplicates</Text>
                      <Badge tone="critical">3 Found</Badge>
                    </InlineStack>
                  </BlockStack>
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingMd">
                    Quick Actions
                  </Text>
                  <BlockStack gap="300">
                    <RemixLink to="/app/maintenance/duplicates">
                      <Button fullWidth>
                        Find Duplicates
                      </Button>
                    </RemixLink>
                    <RemixLink to="/app/maintenance/cleanup">
                      <Button fullWidth variant="secondary">
                        Run Cleanup
                      </Button>
                    </RemixLink>
                    <RemixLink to="/app/maintenance/health">
                      <Button fullWidth variant="secondary">
                        Health Check
                      </Button>
                    </RemixLink>
                  </BlockStack>
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingMd">
                    Maintenance Tips
                  </Text>
                  <BlockStack gap="200">
                    <Text variant="bodySm" tone="subdued">• Run duplicate detection monthly</Text>
                    <Text variant="bodySm" tone="subdued">• Clean up unused tags regularly</Text>
                    <Text variant="bodySm" tone="subdued">• Monitor performance metrics</Text>
                    <Text variant="bodySm" tone="subdued">• Validate data after imports</Text>
                    <Text variant="bodySm" tone="subdued">• Check for broken links quarterly</Text>
                  </BlockStack>
                </BlockStack>
              </Card>
            </BlockStack>
          </Layout.Section>
        </Layout>
      </BlockStack>
    </Page>
  );
}
