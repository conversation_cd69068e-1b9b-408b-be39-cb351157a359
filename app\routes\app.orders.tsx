import type { LoaderFunctionArgs } from "@remix-run/node";
import { Link as RemixLink } from "@remix-run/react";
import {
  Page,
  Layout,
  Text,
  Card,
  Button,
  BlockStack,
  InlineStack,
  Grid,
  Icon,
  Badge,
} from "@shopify/polaris";
import { 
  OrderIcon,
  AutomationIcon,
  AnalyticsIcon,
  FulfillmentIcon,
  RefreshIcon,
  ExportIcon
} from "@shopify/polaris-icons";
import { TitleBar } from "@shopify/app-bridge-react";
import { authenticate } from "../shopify.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  await authenticate.admin(request);
  return null;
};

export default function OrdersIndex() {
  const orderTools = [
    {
      title: "Order Automation",
      description: "Automate order processing with smart tagging, fulfillment assignment, and workflow triggers",
      icon: AutomationIcon,
      url: "/app/orders/automation",
      features: ["Auto-tag orders", "Smart fulfillment", "Workflow triggers", "Rule-based processing"],
      status: "ready"
    },
    {
      title: "Bulk Order Processing",
      description: "Process multiple orders simultaneously with bulk status updates and notifications",
      icon: OrderIcon,
      url: "/app/orders/bulk-processing",
      features: ["Bulk status updates", "Mass notifications", "Batch fulfillment", "Order grouping"],
      status: "ready"
    },
    {
      title: "Order Analytics",
      description: "Comprehensive order analytics with custom metrics, trends, and performance insights",
      icon: AnalyticsIcon,
      url: "/app/orders/analytics",
      features: ["Custom dashboards", "Trend analysis", "Performance metrics", "Revenue insights"],
      status: "ready"
    },
    {
      title: "Fulfillment Optimizer",
      description: "Optimize shipping routes, methods, and fulfillment locations for cost efficiency",
      icon: FulfillmentIcon,
      url: "/app/orders/fulfillment",
      features: ["Route optimization", "Cost analysis", "Location assignment", "Shipping rules"],
      status: "beta"
    },
    {
      title: "Return Management",
      description: "Streamlined return processing with automated workflows and tracking",
      icon: RefreshIcon,
      url: "/app/orders/returns",
      features: ["Return automation", "Tracking system", "Refund processing", "Customer notifications"],
      status: "beta"
    },
    {
      title: "Order Export Tools",
      description: "Export order data with custom filtering, formatting, and scheduling options",
      icon: ExportIcon,
      url: "/app/orders/export",
      features: ["Custom filters", "Multiple formats", "Scheduled exports", "Data mapping"],
      status: "ready"
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "ready":
        return <Badge tone="success">Ready</Badge>;
      case "beta":
        return <Badge tone="attention">Beta</Badge>;
      case "coming-soon":
        return <Badge>Coming Soon</Badge>;
      default:
        return <Badge>Unknown</Badge>;
    }
  };

  return (
    <Page>
      <TitleBar title="Order Management Tools" />
      <BlockStack gap="500">
        <Layout>
          <Layout.Section>
            <Card>
              <BlockStack gap="400">
                <BlockStack gap="200">
                  <InlineStack align="space-between">
                    <InlineStack gap="200" align="center">
                      <Icon source={OrderIcon} tone="base" />
                      <Text as="h2" variant="headingLg">
                        Order Management Utilities
                      </Text>
                    </InlineStack>
                    <RemixLink to="/app">
                      <Button variant="plain">← Back to Dashboard</Button>
                    </RemixLink>
                  </InlineStack>
                  <Text variant="bodyMd" tone="subdued">
                    Streamline your order processing with powerful automation, analytics, and bulk operations. 
                    Optimize fulfillment, manage returns, and gain insights into your order performance.
                  </Text>
                </BlockStack>

                <Grid>
                  {orderTools.map((tool, index) => (
                    <Grid.Cell key={index} columnSpan={{ xs: 6, sm: 6, md: 4, lg: 4, xl: 4 }}>
                      <Card>
                        <BlockStack gap="400">
                          <InlineStack align="space-between">
                            <Icon source={tool.icon} tone="base" />
                            {getStatusBadge(tool.status)}
                          </InlineStack>
                          
                          <BlockStack gap="200">
                            <Text as="h3" variant="headingMd">
                              {tool.title}
                            </Text>
                            <Text variant="bodyMd" tone="subdued">
                              {tool.description}
                            </Text>
                          </BlockStack>

                          <BlockStack gap="200">
                            <Text variant="headingSm">Key Features:</Text>
                            <BlockStack gap="100">
                              {tool.features.map((feature, featureIndex) => (
                                <Text key={featureIndex} variant="bodySm" tone="subdued">
                                  • {feature}
                                </Text>
                              ))}
                            </BlockStack>
                          </BlockStack>

                          <InlineStack align="end">
                            <RemixLink to={tool.url}>
                              <Button variant="primary" size="slim">
                                Open Tool →
                              </Button>
                            </RemixLink>
                          </InlineStack>
                        </BlockStack>
                      </Card>
                    </Grid.Cell>
                  ))}
                </Grid>
              </BlockStack>
            </Card>
          </Layout.Section>

          <Layout.Section variant="oneThird">
            <BlockStack gap="500">
              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingMd">
                    Quick Actions
                  </Text>
                  <BlockStack gap="300">
                    <RemixLink to="/app/orders/automation">
                      <Button fullWidth>
                        Setup Automation
                      </Button>
                    </RemixLink>
                    <RemixLink to="/app/orders/bulk-processing">
                      <Button fullWidth variant="secondary">
                        Bulk Process Orders
                      </Button>
                    </RemixLink>
                    <RemixLink to="/app/orders/analytics">
                      <Button fullWidth variant="secondary">
                        View Analytics
                      </Button>
                    </RemixLink>
                  </BlockStack>
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingMd">
                    Order Stats
                  </Text>
                  <BlockStack gap="200">
                    <InlineStack align="space-between">
                      <Text variant="bodyMd">Available Tools</Text>
                      <Text variant="bodyMd" fontWeight="semibold">{orderTools.length}</Text>
                    </InlineStack>
                    <InlineStack align="space-between">
                      <Text variant="bodyMd">Ready Tools</Text>
                      <Text variant="bodyMd" fontWeight="semibold">
                        {orderTools.filter(tool => tool.status === "ready").length}
                      </Text>
                    </InlineStack>
                    <InlineStack align="space-between">
                      <Text variant="bodyMd">Beta Tools</Text>
                      <Text variant="bodyMd" fontWeight="semibold">
                        {orderTools.filter(tool => tool.status === "beta").length}
                      </Text>
                    </InlineStack>
                  </BlockStack>
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingMd">
                    Getting Started
                  </Text>
                  <Text variant="bodyMd" tone="subdued">
                    Start with Order Automation to set up rules for automatic processing, 
                    then use Bulk Processing for handling multiple orders efficiently.
                  </Text>
                  <RemixLink to="/app/orders/automation">
                    <Button variant="plain">
                      Start with Automation →
                    </Button>
                  </RemixLink>
                </BlockStack>
              </Card>
            </BlockStack>
          </Layout.Section>
        </Layout>
      </BlockStack>
    </Page>
  );
}
