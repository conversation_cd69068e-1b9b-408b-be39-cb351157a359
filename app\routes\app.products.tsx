import type { LoaderFunctionArgs } from "@remix-run/node";
import { Link as RemixLink } from "@remix-run/react";
import {
  Page,
  Layout,
  Text,
  Card,
  Button,
  BlockStack,
  InlineStack,
  Grid,
  Icon,
  Badge,
} from "@shopify/polaris";
import { 
  ProductIcon,
  EditIcon,
  InventoryIcon,
  ImageIcon,
  VariantIcon,
  CashDollarIcon
} from "@shopify/polaris-icons";
import { TitleBar } from "@shopify/app-bridge-react";
import { authenticate } from "../shopify.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  await authenticate.admin(request);
  return null;
};

export default function ProductsIndex() {
  const productTools = [
    {
      title: "Bulk Product Editor",
      description: "Edit multiple products at once - titles, descriptions, prices, tags, and SEO fields",
      icon: EditIcon,
      url: "/app/products/bulk-editor",
      features: ["Mass edit titles & descriptions", "Bulk price updates", "Tag management", "SEO optimization"],
      status: "ready"
    },
    {
      title: "Inventory Sync Tool",
      description: "Synchronize inventory across multiple locations and sales channels",
      icon: InventoryIcon,
      url: "/app/products/inventory-sync",
      features: ["Multi-location sync", "Channel inventory", "Stock alerts", "Reorder automation"],
      status: "ready"
    },
    {
      title: "Product Image Optimizer",
      description: "Compress, resize, and optimize product images for better performance",
      icon: ImageIcon,
      url: "/app/products/image-optimizer",
      features: ["Image compression", "Auto-resize", "Format conversion", "Alt text generation"],
      status: "ready"
    },
    {
      title: "Variant Manager",
      description: "Bulk create and edit product variants and options efficiently",
      icon: VariantIcon,
      url: "/app/products/variant-manager",
      features: ["Bulk variant creation", "Option management", "Pricing rules", "Inventory tracking"],
      status: "ready"
    },
    {
      title: "Price Calculator",
      description: "Dynamic pricing rules and bulk price updates with profit margin analysis",
      icon: CashDollarIcon,
      url: "/app/products/price-calculator",
      features: ["Dynamic pricing", "Margin analysis", "Competitor tracking", "Bulk updates"],
      status: "ready"
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "ready":
        return <Badge tone="success">Ready</Badge>;
      case "beta":
        return <Badge tone="attention">Beta</Badge>;
      case "coming-soon":
        return <Badge>Coming Soon</Badge>;
      default:
        return <Badge>Unknown</Badge>;
    }
  };

  return (
    <Page>
      <TitleBar title="Product Management Tools" />
      <BlockStack gap="500">
        <Layout>
          <Layout.Section>
            <Card>
              <BlockStack gap="400">
                <BlockStack gap="200">
                  <InlineStack align="space-between">
                    <InlineStack gap="200" align="center">
                      <Icon source={ProductIcon} tone="base" />
                      <Text as="h2" variant="headingLg">
                        Product Management Utilities
                      </Text>
                    </InlineStack>
                    <RemixLink to="/app">
                      <Button variant="plain">← Back to Dashboard</Button>
                    </RemixLink>
                  </InlineStack>
                  <Text variant="bodyMd" tone="subdued">
                    Powerful tools to manage your products efficiently. Bulk edit, optimize images, 
                    sync inventory, and manage variants with ease.
                  </Text>
                </BlockStack>

                <Grid>
                  {productTools.map((tool, index) => (
                    <Grid.Cell key={index} columnSpan={{ xs: 6, sm: 6, md: 4, lg: 4, xl: 4 }}>
                      <Card>
                        <BlockStack gap="400">
                          <InlineStack align="space-between">
                            <Icon source={tool.icon} tone="base" />
                            {getStatusBadge(tool.status)}
                          </InlineStack>
                          
                          <BlockStack gap="200">
                            <Text as="h3" variant="headingMd">
                              {tool.title}
                            </Text>
                            <Text variant="bodyMd" tone="subdued">
                              {tool.description}
                            </Text>
                          </BlockStack>

                          <BlockStack gap="200">
                            <Text variant="headingSm">Key Features:</Text>
                            <BlockStack gap="100">
                              {tool.features.map((feature, featureIndex) => (
                                <Text key={featureIndex} variant="bodySm" tone="subdued">
                                  • {feature}
                                </Text>
                              ))}
                            </BlockStack>
                          </BlockStack>

                          <InlineStack align="end">
                            <RemixLink to={tool.url}>
                              <Button variant="primary" size="slim">
                                Open Tool →
                              </Button>
                            </RemixLink>
                          </InlineStack>
                        </BlockStack>
                      </Card>
                    </Grid.Cell>
                  ))}
                </Grid>
              </BlockStack>
            </Card>
          </Layout.Section>

          <Layout.Section variant="oneThird">
            <BlockStack gap="500">
              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingMd">
                    Quick Actions
                  </Text>
                  <BlockStack gap="300">
                    <RemixLink to="/app/products/bulk-editor">
                      <Button fullWidth>
                        Start Bulk Editing
                      </Button>
                    </RemixLink>
                    <RemixLink to="/app/products/inventory-sync">
                      <Button fullWidth variant="secondary">
                        Sync Inventory
                      </Button>
                    </RemixLink>
                    <RemixLink to="/app/products/image-optimizer">
                      <Button fullWidth variant="secondary">
                        Optimize Images
                      </Button>
                    </RemixLink>
                  </BlockStack>
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingMd">
                    Product Stats
                  </Text>
                  <BlockStack gap="200">
                    <InlineStack align="space-between">
                      <Text variant="bodyMd">Available Tools</Text>
                      <Text variant="bodyMd" fontWeight="semibold">{productTools.length}</Text>
                    </InlineStack>
                    <InlineStack align="space-between">
                      <Text variant="bodyMd">Ready Tools</Text>
                      <Text variant="bodyMd" fontWeight="semibold">
                        {productTools.filter(tool => tool.status === "ready").length}
                      </Text>
                    </InlineStack>
                    <InlineStack align="space-between">
                      <Text variant="bodyMd">Total Features</Text>
                      <Text variant="bodyMd" fontWeight="semibold">
                        {productTools.reduce((acc, tool) => acc + tool.features.length, 0)}
                      </Text>
                    </InlineStack>
                  </BlockStack>
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingMd">
                    Getting Started
                  </Text>
                  <Text variant="bodyMd" tone="subdued">
                    New to product management? Start with the Bulk Product Editor to get familiar 
                    with mass editing capabilities, then explore inventory sync and image optimization.
                  </Text>
                  <RemixLink to="/app/products/bulk-editor">
                    <Button variant="plain">
                      Start with Bulk Editor →
                    </Button>
                  </RemixLink>
                </BlockStack>
              </Card>
            </BlockStack>
          </Layout.Section>
        </Layout>
      </BlockStack>
    </Page>
  );
}
