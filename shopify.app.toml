# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "661c4a109601a4a466be509f03177540"
application_url = "https://example.com/"
embedded = true
name = "Swiss Knife Utility App"
handle = "swiss-knife-utility-app"

[build]
include_config_on_deploy = true

[webhooks]
api_version = "2025-04"

  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled" ]
  uri = "/webhooks/app/uninstalled"

  [[webhooks.subscriptions]]
  topics = [ "app/scopes_update" ]
  uri = "/webhooks/app/scopes_update"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "write_products"

[auth]
redirect_urls = [ "https://example.com/api/auth" ]

[pos]
embedded = false
