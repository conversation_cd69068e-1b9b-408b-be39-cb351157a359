import type { LoaderFunctionArgs } from "@remix-run/node";
import { Link as RemixLink } from "@remix-run/react";
import {
  Page,
  Layout,
  Text,
  Card,
  Button,
  BlockStack,
  InlineStack,
  Grid,
  Icon,
  Badge,
} from "@shopify/polaris";
import { 
  CustomerIcon,
  AnalyticsIcon,
  EditIcon,
  CleanIcon,
  EmailIcon,
  SegmentIcon
} from "@shopify/polaris-icons";
import { TitleBar } from "@shopify/app-bridge-react";
import { authenticate } from "../shopify.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  await authenticate.admin(request);
  return null;
};

export default function CustomersIndex() {
  const customerTools = [
    {
      title: "Customer Segmentation",
      description: "Create advanced customer segments based on behavior, purchase history, and demographics",
      icon: SegmentIcon,
      url: "/app/customers/segmentation",
      features: ["Behavioral segmentation", "Purchase history analysis", "Custom criteria", "Auto-updating segments"],
      status: "ready"
    },
    {
      title: "Bulk Customer Operations",
      description: "Perform bulk operations on customers including tagging, tier assignments, and communications",
      icon: EditIcon,
      url: "/app/customers/bulk-operations",
      features: ["Mass tagging", "Tier assignments", "Bulk email", "Data updates"],
      status: "ready"
    },
    {
      title: "Customer Analytics",
      description: "Comprehensive customer analytics with lifetime value, retention, and behavior insights",
      icon: AnalyticsIcon,
      url: "/app/customers/analytics",
      features: ["CLV calculation", "Retention analysis", "Behavior tracking", "Cohort analysis"],
      status: "ready"
    },
    {
      title: "Customer Data Cleaner",
      description: "Clean and standardize customer data by removing duplicates and fixing inconsistencies",
      icon: CleanIcon,
      url: "/app/customers/data-cleaner",
      features: ["Duplicate detection", "Data standardization", "Address validation", "Contact cleanup"],
      status: "beta"
    },
    {
      title: "Loyalty Points Manager",
      description: "Manage custom loyalty programs with points, rewards, and tier management",
      icon: CustomerIcon,
      url: "/app/customers/loyalty",
      features: ["Points management", "Reward tracking", "Tier systems", "Custom rules"],
      status: "beta"
    },
    {
      title: "Customer Export Tools",
      description: "Export customer data with advanced filtering and custom field selection",
      icon: EmailIcon,
      url: "/app/customers/export",
      features: ["Advanced filters", "Custom fields", "Multiple formats", "Scheduled exports"],
      status: "ready"
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "ready":
        return <Badge tone="success">Ready</Badge>;
      case "beta":
        return <Badge tone="attention">Beta</Badge>;
      case "coming-soon":
        return <Badge>Coming Soon</Badge>;
      default:
        return <Badge>Unknown</Badge>;
    }
  };

  return (
    <Page>
      <TitleBar title="Customer Management Tools" />
      <BlockStack gap="500">
        <Layout>
          <Layout.Section>
            <Card>
              <BlockStack gap="400">
                <BlockStack gap="200">
                  <InlineStack align="space-between">
                    <InlineStack gap="200" align="center">
                      <Icon source={CustomerIcon} tone="base" />
                      <Text as="h2" variant="headingLg">
                        Customer Management Utilities
                      </Text>
                    </InlineStack>
                    <RemixLink to="/app">
                      <Button variant="plain">← Back to Dashboard</Button>
                    </RemixLink>
                  </InlineStack>
                  <Text variant="bodyMd" tone="subdued">
                    Powerful tools to manage your customer relationships. Segment customers, analyze behavior, 
                    perform bulk operations, and maintain clean customer data.
                  </Text>
                </BlockStack>

                <Grid>
                  {customerTools.map((tool, index) => (
                    <Grid.Cell key={index} columnSpan={{ xs: 6, sm: 6, md: 4, lg: 4, xl: 4 }}>
                      <Card>
                        <BlockStack gap="400">
                          <InlineStack align="space-between">
                            <Icon source={tool.icon} tone="base" />
                            {getStatusBadge(tool.status)}
                          </InlineStack>
                          
                          <BlockStack gap="200">
                            <Text as="h3" variant="headingMd">
                              {tool.title}
                            </Text>
                            <Text variant="bodyMd" tone="subdued">
                              {tool.description}
                            </Text>
                          </BlockStack>

                          <BlockStack gap="200">
                            <Text variant="headingSm">Key Features:</Text>
                            <BlockStack gap="100">
                              {tool.features.map((feature, featureIndex) => (
                                <Text key={featureIndex} variant="bodySm" tone="subdued">
                                  • {feature}
                                </Text>
                              ))}
                            </BlockStack>
                          </BlockStack>

                          <InlineStack align="end">
                            <RemixLink to={tool.url}>
                              <Button variant="primary" size="slim">
                                Open Tool →
                              </Button>
                            </RemixLink>
                          </InlineStack>
                        </BlockStack>
                      </Card>
                    </Grid.Cell>
                  ))}
                </Grid>
              </BlockStack>
            </Card>
          </Layout.Section>

          <Layout.Section variant="oneThird">
            <BlockStack gap="500">
              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingMd">
                    Quick Actions
                  </Text>
                  <BlockStack gap="300">
                    <RemixLink to="/app/customers/segmentation">
                      <Button fullWidth>
                        Create Segments
                      </Button>
                    </RemixLink>
                    <RemixLink to="/app/customers/bulk-operations">
                      <Button fullWidth variant="secondary">
                        Bulk Operations
                      </Button>
                    </RemixLink>
                    <RemixLink to="/app/customers/analytics">
                      <Button fullWidth variant="secondary">
                        View Analytics
                      </Button>
                    </RemixLink>
                  </BlockStack>
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingMd">
                    Customer Stats
                  </Text>
                  <BlockStack gap="200">
                    <InlineStack align="space-between">
                      <Text variant="bodyMd">Available Tools</Text>
                      <Text variant="bodyMd" fontWeight="semibold">{customerTools.length}</Text>
                    </InlineStack>
                    <InlineStack align="space-between">
                      <Text variant="bodyMd">Ready Tools</Text>
                      <Text variant="bodyMd" fontWeight="semibold">
                        {customerTools.filter(tool => tool.status === "ready").length}
                      </Text>
                    </InlineStack>
                    <InlineStack align="space-between">
                      <Text variant="bodyMd">Beta Tools</Text>
                      <Text variant="bodyMd" fontWeight="semibold">
                        {customerTools.filter(tool => tool.status === "beta").length}
                      </Text>
                    </InlineStack>
                  </BlockStack>
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingMd">
                    Getting Started
                  </Text>
                  <Text variant="bodyMd" tone="subdued">
                    Begin with Customer Segmentation to organize your customers into meaningful groups, 
                    then use Analytics to understand their behavior and value.
                  </Text>
                  <RemixLink to="/app/customers/segmentation">
                    <Button variant="plain">
                      Start with Segmentation →
                    </Button>
                  </RemixLink>
                </BlockStack>
              </Card>
            </BlockStack>
          </Layout.Section>
        </Layout>
      </BlockStack>
    </Page>
  );
}
