import type { LoaderFunctionArgs } from "@remix-run/node";
import { Link as RemixLink } from "@remix-run/react";
import {
  Page,
  Layout,
  Text,
  Card,
  Button,
  BlockStack,
  InlineStack,
  Grid,
  Icon,
  Badge,
} from "@shopify/polaris";
import { 
  ImportIcon,
  ExportIcon,
  BackupIcon,
  TransferIcon,
  FileIcon,
  CalendarIcon
} from "@shopify/polaris-icons";
import { TitleBar } from "@shopify/app-bridge-react";
import { authenticate } from "../shopify.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  await authenticate.admin(request);
  return null;
};

export default function DataIndex() {
  const dataTools = [
    {
      title: "Universal Importer",
      description: "Import data from CSV, JSON, XML files with intelligent field mapping",
      icon: ImportIcon,
      url: "/app/data/import",
      features: ["Multi-format support", "Field mapping", "Data validation", "Batch processing"],
      status: "ready"
    },
    {
      title: "Custom Exports",
      description: "Export store data with advanced filtering and custom field selection",
      icon: ExportIcon,
      url: "/app/data/export",
      features: ["Custom filters", "Field selection", "Multiple formats", "Scheduled exports"],
      status: "ready"
    },
    {
      title: "Backup Creator",
      description: "Create comprehensive backups of your store data with automated scheduling",
      icon: BackupIcon,
      url: "/app/data/backup",
      features: ["Full store backup", "Incremental backups", "Automated scheduling", "Cloud storage"],
      status: "ready"
    },
    {
      title: "Migration Assistant",
      description: "Migrate data from other platforms with guided setup and validation",
      icon: TransferIcon,
      url: "/app/data/migration",
      features: ["Platform migration", "Data mapping", "Validation tools", "Progress tracking"],
      status: "beta"
    },
    {
      title: "Data Validator",
      description: "Validate and clean your data to ensure consistency and accuracy",
      icon: FileIcon,
      url: "/app/data/validator",
      features: ["Data validation", "Error detection", "Cleanup suggestions", "Quality reports"],
      status: "ready"
    },
    {
      title: "Scheduled Operations",
      description: "Schedule recurring data operations and automated maintenance tasks",
      icon: CalendarIcon,
      url: "/app/data/scheduler",
      features: ["Recurring tasks", "Automated operations", "Task monitoring", "Email notifications"],
      status: "beta"
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "ready":
        return <Badge tone="success">Ready</Badge>;
      case "beta":
        return <Badge tone="attention">Beta</Badge>;
      case "coming-soon":
        return <Badge>Coming Soon</Badge>;
      default:
        return <Badge>Unknown</Badge>;
    }
  };

  return (
    <Page>
      <TitleBar title="Data Import/Export Tools" />
      <BlockStack gap="500">
        <Layout>
          <Layout.Section>
            <Card>
              <BlockStack gap="400">
                <BlockStack gap="200">
                  <InlineStack align="space-between">
                    <InlineStack gap="200" align="center">
                      <Icon source={ImportIcon} tone="base" />
                      <Text as="h2" variant="headingLg">
                        Data Import/Export Utilities
                      </Text>
                    </InlineStack>
                    <RemixLink to="/app">
                      <Button variant="plain">← Back to Dashboard</Button>
                    </RemixLink>
                  </InlineStack>
                  <Text variant="bodyMd" tone="subdued">
                    Powerful data management tools for importing, exporting, backing up, and migrating your store data. 
                    Handle large datasets with ease and maintain data integrity.
                  </Text>
                </BlockStack>

                <Grid>
                  {dataTools.map((tool, index) => (
                    <Grid.Cell key={index} columnSpan={{ xs: 6, sm: 6, md: 4, lg: 4, xl: 4 }}>
                      <Card>
                        <BlockStack gap="400">
                          <InlineStack align="space-between">
                            <Icon source={tool.icon} tone="base" />
                            {getStatusBadge(tool.status)}
                          </InlineStack>
                          
                          <BlockStack gap="200">
                            <Text as="h3" variant="headingMd">
                              {tool.title}
                            </Text>
                            <Text variant="bodyMd" tone="subdued">
                              {tool.description}
                            </Text>
                          </BlockStack>

                          <BlockStack gap="200">
                            <Text variant="headingSm">Key Features:</Text>
                            <BlockStack gap="100">
                              {tool.features.map((feature, featureIndex) => (
                                <Text key={featureIndex} variant="bodySm" tone="subdued">
                                  • {feature}
                                </Text>
                              ))}
                            </BlockStack>
                          </BlockStack>

                          <InlineStack align="end">
                            <RemixLink to={tool.url}>
                              <Button variant="primary" size="slim">
                                Open Tool →
                              </Button>
                            </RemixLink>
                          </InlineStack>
                        </BlockStack>
                      </Card>
                    </Grid.Cell>
                  ))}
                </Grid>
              </BlockStack>
            </Card>
          </Layout.Section>

          <Layout.Section variant="oneThird">
            <BlockStack gap="500">
              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingMd">
                    Quick Actions
                  </Text>
                  <BlockStack gap="300">
                    <RemixLink to="/app/data/import">
                      <Button fullWidth>
                        Import Data
                      </Button>
                    </RemixLink>
                    <RemixLink to="/app/data/export">
                      <Button fullWidth variant="secondary">
                        Export Data
                      </Button>
                    </RemixLink>
                    <RemixLink to="/app/data/backup">
                      <Button fullWidth variant="secondary">
                        Create Backup
                      </Button>
                    </RemixLink>
                  </BlockStack>
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingMd">
                    Recent Activity
                  </Text>
                  <BlockStack gap="200">
                    <Text variant="bodySm" tone="subdued">• Product export completed (2 hours ago)</Text>
                    <Text variant="bodySm" tone="subdued">• Customer data imported (Yesterday)</Text>
                    <Text variant="bodySm" tone="subdued">• Backup created (3 days ago)</Text>
                    <Text variant="bodySm" tone="subdued">• Order export scheduled (Weekly)</Text>
                  </BlockStack>
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingMd">
                    Data Stats
                  </Text>
                  <BlockStack gap="200">
                    <InlineStack align="space-between">
                      <Text variant="bodyMd">Available Tools</Text>
                      <Text variant="bodyMd" fontWeight="semibold">{dataTools.length}</Text>
                    </InlineStack>
                    <InlineStack align="space-between">
                      <Text variant="bodyMd">Last Backup</Text>
                      <Text variant="bodyMd" fontWeight="semibold">3 days ago</Text>
                    </InlineStack>
                    <InlineStack align="space-between">
                      <Text variant="bodyMd">Storage Used</Text>
                      <Text variant="bodyMd" fontWeight="semibold">2.4 GB</Text>
                    </InlineStack>
                  </BlockStack>
                </BlockStack>
              </Card>
            </BlockStack>
          </Layout.Section>
        </Layout>
      </BlockStack>
    </Page>
  );
}
