import { useState, useCallback } from "react";
import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useActionData, useSubmit, Link as RemixLink } from "@remix-run/react";
import {
  Page,
  Layout,
  Text,
  Card,
  Button,
  BlockStack,
  InlineStack,
  TextField,
  Select,
  Checkbox,
  Banner,
  Modal,
  TextContainer,
  Badge,
  DataTable,
} from "@shopify/polaris";
import { TitleBar, useAppBridge } from "@shopify/app-bridge-react";
import { authenticate } from "../shopify.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  await authenticate.admin(request);
  return json({ 
    automationRules: [
      {
        id: "1",
        name: "High Value Order Tagging",
        trigger: "Order value > $500",
        action: "Add 'high-value' tag",
        status: "active",
        lastRun: "2024-01-15"
      },
      {
        id: "2", 
        name: "Express Shipping Auto-fulfill",
        trigger: "Shipping method contains 'Express'",
        action: "Auto-assign to main warehouse",
        status: "active",
        lastRun: "2024-01-15"
      }
    ]
  });
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const { admin } = await authenticate.admin(request);
  const formData = await request.formData();
  const action = formData.get("action");

  if (action === "createRule") {
    const ruleData = {
      name: formData.get("name"),
      trigger: formData.get("trigger"),
      triggerValue: formData.get("triggerValue"),
      actionType: formData.get("actionType"),
      actionValue: formData.get("actionValue")
    };

    // In a real implementation, you would save this to your database
    // and set up the actual automation logic
    
    return json({ 
      success: true, 
      message: `Automation rule "${ruleData.name}" created successfully`
    });
  }

  if (action === "toggleRule") {
    const ruleId = formData.get("ruleId");
    const newStatus = formData.get("status");
    
    return json({ 
      success: true, 
      message: `Rule ${newStatus === "active" ? "activated" : "deactivated"} successfully`
    });
  }

  return json({ success: false, message: "Invalid action" });
};

export default function OrderAutomation() {
  const actionData = useActionData<typeof action>();
  const submit = useSubmit();
  const shopify = useAppBridge();
  
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [newRule, setNewRule] = useState({
    name: "",
    trigger: "",
    triggerValue: "",
    actionType: "",
    actionValue: ""
  });

  const handleCreateRule = useCallback(() => {
    if (!newRule.name || !newRule.trigger || !newRule.actionType) {
      shopify.toast.show("Please fill in all required fields", { isError: true });
      return;
    }

    const formData = new FormData();
    formData.append("action", "createRule");
    Object.entries(newRule).forEach(([key, value]) => {
      formData.append(key, value);
    });
    
    submit(formData, { method: "post" });
    setShowCreateModal(false);
    setNewRule({
      name: "",
      trigger: "",
      triggerValue: "",
      actionType: "",
      actionValue: ""
    });
  }, [newRule, submit, shopify]);

  const automationRows = [
    [
      "High Value Order Tagging",
      <Badge key="1" tone="success">Active</Badge>,
      "Order value > $500",
      "Add 'high-value' tag",
      "2024-01-15"
    ],
    [
      "Express Shipping Auto-fulfill", 
      <Badge key="2" tone="success">Active</Badge>,
      "Shipping method contains 'Express'",
      "Auto-assign to main warehouse",
      "2024-01-15"
    ],
    [
      "Weekend Order Hold",
      <Badge key="3">Inactive</Badge>,
      "Order placed on weekend",
      "Add 'weekend-hold' tag",
      "2024-01-10"
    ]
  ];

  return (
    <Page>
      <TitleBar title="Order Automation" />
      <BlockStack gap="500">
        {actionData?.success && (
          <Banner tone="success" title="Success">
            <p>{actionData.message}</p>
          </Banner>
        )}
        
        {actionData?.success === false && (
          <Banner tone="critical" title="Error">
            <p>{actionData.message}</p>
          </Banner>
        )}

        <Layout>
          <Layout.Section>
            <Card>
              <BlockStack gap="400">
                <InlineStack align="space-between">
                  <Text as="h2" variant="headingLg">
                    Order Automation Rules
                  </Text>
                  <InlineStack gap="200">
                    <Button variant="primary" onClick={() => setShowCreateModal(true)}>
                      Create New Rule
                    </Button>
                    <RemixLink to="/app/orders">
                      <Button variant="plain">← Back to Orders</Button>
                    </RemixLink>
                  </InlineStack>
                </InlineStack>
                
                <Text variant="bodyMd" tone="subdued">
                  Automate your order processing with custom rules. Set up triggers based on order properties 
                  and define actions to be taken automatically.
                </Text>

                <DataTable
                  columnContentTypes={['text', 'text', 'text', 'text', 'text']}
                  headings={['Rule Name', 'Status', 'Trigger', 'Action', 'Last Run']}
                  rows={automationRows}
                />
              </BlockStack>
            </Card>
          </Layout.Section>

          <Layout.Section variant="oneThird">
            <BlockStack gap="500">
              <Card>
                <BlockStack gap="300">
                  <Text as="h3" variant="headingMd">
                    Automation Stats
                  </Text>
                  <BlockStack gap="200">
                    <InlineStack align="space-between">
                      <Text variant="bodyMd">Total Rules</Text>
                      <Badge tone="info">3</Badge>
                    </InlineStack>
                    <InlineStack align="space-between">
                      <Text variant="bodyMd">Active Rules</Text>
                      <Badge tone="success">2</Badge>
                    </InlineStack>
                    <InlineStack align="space-between">
                      <Text variant="bodyMd">Orders Processed Today</Text>
                      <Badge>47</Badge>
                    </InlineStack>
                  </BlockStack>
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingMd">
                    Popular Triggers
                  </Text>
                  <BlockStack gap="200">
                    <Text variant="bodySm" tone="subdued">• Order value thresholds</Text>
                    <Text variant="bodySm" tone="subdued">• Shipping method selection</Text>
                    <Text variant="bodySm" tone="subdued">• Customer tags</Text>
                    <Text variant="bodySm" tone="subdued">• Product categories</Text>
                    <Text variant="bodySm" tone="subdued">• Geographic location</Text>
                  </BlockStack>
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="200">
                  <Text as="h3" variant="headingMd">
                    Common Actions
                  </Text>
                  <BlockStack gap="200">
                    <Text variant="bodySm" tone="subdued">• Add order tags</Text>
                    <Text variant="bodySm" tone="subdued">• Assign fulfillment location</Text>
                    <Text variant="bodySm" tone="subdued">• Send notifications</Text>
                    <Text variant="bodySm" tone="subdued">• Update order priority</Text>
                    <Text variant="bodySm" tone="subdued">• Create internal notes</Text>
                  </BlockStack>
                </BlockStack>
              </Card>
            </BlockStack>
          </Layout.Section>
        </Layout>

        <Modal
          open={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          title="Create Automation Rule"
          primaryAction={{
            content: "Create Rule",
            onAction: handleCreateRule
          }}
          secondaryActions={[
            {
              content: "Cancel",
              onAction: () => setShowCreateModal(false)
            }
          ]}
        >
          <Modal.Section>
            <BlockStack gap="400">
              <TextField
                label="Rule Name"
                value={newRule.name}
                onChange={(value) => setNewRule(prev => ({ ...prev, name: value }))}
                placeholder="e.g., High Value Order Processing"
                autoComplete="off"
              />
              
              <Select
                label="Trigger Type"
                options={[
                  { label: "Select trigger...", value: "" },
                  { label: "Order value", value: "order_value" },
                  { label: "Shipping method", value: "shipping_method" },
                  { label: "Customer tag", value: "customer_tag" },
                  { label: "Product type", value: "product_type" },
                  { label: "Order location", value: "order_location" }
                ]}
                value={newRule.trigger}
                onChange={(value) => setNewRule(prev => ({ ...prev, trigger: value }))}
              />
              
              <TextField
                label="Trigger Value"
                value={newRule.triggerValue}
                onChange={(value) => setNewRule(prev => ({ ...prev, triggerValue: value }))}
                placeholder="e.g., > 500, contains 'Express', etc."
                autoComplete="off"
              />
              
              <Select
                label="Action Type"
                options={[
                  { label: "Select action...", value: "" },
                  { label: "Add order tag", value: "add_tag" },
                  { label: "Assign fulfillment location", value: "assign_location" },
                  { label: "Send notification", value: "send_notification" },
                  { label: "Update priority", value: "update_priority" },
                  { label: "Add note", value: "add_note" }
                ]}
                value={newRule.actionType}
                onChange={(value) => setNewRule(prev => ({ ...prev, actionType: value }))}
              />
              
              <TextField
                label="Action Value"
                value={newRule.actionValue}
                onChange={(value) => setNewRule(prev => ({ ...prev, actionValue: value }))}
                placeholder="e.g., 'high-value', 'Main Warehouse', etc."
                autoComplete="off"
              />
            </BlockStack>
          </Modal.Section>
        </Modal>
      </BlockStack>
    </Page>
  );
}
